package com.example.jackmoneytransafer.services;

import com.example.jackmoneytransafer.ecddservice.dto.TransactionGenericRequest;
import com.example.jackmoneytransafer.ecddservice.entity.TransactionRequestBO;
import com.example.jackmoneytransafer.ecddservice.repository.TransactionRequestRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TransactionRequestServiceTest {

    @Mock
    private TransactionRequestRepository transactionRequestRepository;

    @InjectMocks
    private TransactionRequestService transactionRequestService;

    private TransactionRequestBO transactionRequestBO;
    private TransactionGenericRequest transactionGenericRequest;
    private String poeTranEventId;
    private String transactionRequestKey;

    @BeforeEach
    void setUp() {
        poeTranEventId = "EVENT_123";
        transactionRequestKey = "TRAN_GEN_REQ_" + poeTranEventId;

        transactionGenericRequest = new TransactionGenericRequest();
        transactionGenericRequest.setTransactionId("TRANS_123");
        transactionGenericRequest.setSenderId("SENDER_123");
        transactionGenericRequest.setReceiverId("RECEIVER_123");
        transactionGenericRequest.setAmount(new BigDecimal("100.00"));
        transactionGenericRequest.setCurrency("USD");
        transactionGenericRequest.setStatus("COMPLETED");
        transactionGenericRequest.setCreatedDate(LocalDateTime.now());

        transactionRequestBO = new TransactionRequestBO();
        transactionRequestBO.setId("ID_123");
        transactionRequestBO.setTransactionRequestKey(transactionRequestKey);
        transactionRequestBO.setTransactionRequest(transactionGenericRequest);
        transactionRequestBO.setCreatedDate(LocalDateTime.now());
        transactionRequestBO.setStatus("ACTIVE");
    }

    @Test
    void testGetTransactionGenericRequest_Success() {
        // Arrange
        when(transactionRequestRepository.findByTransactionRequestKey(transactionRequestKey))
                .thenReturn(Optional.of(transactionRequestBO));

        // Act
        TransactionGenericRequest result = transactionRequestService.getTransactionGenericRequest(poeTranEventId);

        // Assert
        assertNotNull(result);
        assertEquals(transactionGenericRequest.getTransactionId(), result.getTransactionId());
        assertEquals(transactionGenericRequest.getAmount(), result.getAmount());
        assertEquals(transactionGenericRequest.getCurrency(), result.getCurrency());
        verify(transactionRequestRepository, times(1)).findByTransactionRequestKey(transactionRequestKey);
    }

    @Test
    void testGetTransactionGenericRequest_NotFound() {
        // Arrange
        when(transactionRequestRepository.findByTransactionRequestKey(anyString()))
                .thenReturn(Optional.empty());

        // Act
        TransactionGenericRequest result = transactionRequestService.getTransactionGenericRequest(poeTranEventId);

        // Assert
        assertNull(result);
        verify(transactionRequestRepository, times(1)).findByTransactionRequestKey(transactionRequestKey);
    }

    @Test
    void testGetTransactionGenericRequest_ExceptionHandling() {
        // Arrange
        when(transactionRequestRepository.findByTransactionRequestKey(anyString()))
                .thenThrow(new RuntimeException("Database error"));

        // Act
        TransactionGenericRequest result = transactionRequestService.getTransactionGenericRequest(poeTranEventId);

        // Assert
        assertNull(result);
        verify(transactionRequestRepository, times(1)).findByTransactionRequestKey(transactionRequestKey);
    }
}
