package com.example.jackmoneytransafer.ecddservice.processor;

import com.example.jackmoneytransafer.ecddservice.bo.EntityTxnRequest;
import com.example.jackmoneytransafer.ecddservice.bo.IFMMergeUnMergeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EntityTxnProcessor {
    
    public IFMMergeUnMergeRequest callEntityTxnService(EntityTxnRequest request) {
        log.info("Processing entity transaction request for entityId: {}", 
                request.getEntity() != null ? request.getEntity().getEntityId() : "null");
        
        try {
            // Create response object
            IFMMergeUnMergeRequest response = new IFMMergeUnMergeRequest();
            
            // Create merge/unmerge request
            IFMMergeUnMergeRequest.MergeUnmergeRequest mergeRequest = 
                new IFMMergeUnMergeRequest.MergeUnmergeRequest();
            
            if (request.getEntity() != null) {
                mergeRequest.setEntityId(request.getEntity().getEntityId());
                mergeRequest.setRequestId("REQ_" + System.currentTimeMillis());
                mergeRequest.setMergeType(request.isNbrLocated() ? "MERGE" : "UNMERGE");
                mergeRequest.setStatus(true);
            }
            
            response.setMergeUnMergeRequest(mergeRequest);
            response.setMessageGUID(request.getMessageGUID());
            response.setMergeUnmerge(request.isNbrLocated());
            
            log.info("Successfully processed entity transaction request");
            return response;
            
        } catch (Exception e) {
            log.error("Error processing entity transaction request: {}", e.getMessage(), e);
            return null;
        }
    }
}
