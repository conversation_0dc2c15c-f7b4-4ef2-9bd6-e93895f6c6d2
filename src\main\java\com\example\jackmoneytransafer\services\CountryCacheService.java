package com.example.jackmoneytransafer.services;

import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;

@Service
public class CountryCacheService {
    
    private final Map<String, String> countryCache = new HashMap<>();
    
    public Map<String, String> getCountryMap() {
        // Initialize with sample data
        countryCache.put("US", "United States");
        countryCache.put("CA", "Canada");
        return countryCache;
    }
}