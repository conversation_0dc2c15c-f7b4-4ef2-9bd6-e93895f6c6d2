package com.example.moneytransfer.controller;

/*

import com.example.moneytransfer.dto.MoneyTransferRequest;
import com.example.moneytransfer.service.MoneyTransferService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/money-transfer")
public class MoneyTransferController {

    @Autowired
    private MoneyTransferService moneyTransferService;

    @PostMapping("/transfer")
    public String transferMoney(@RequestBody MoneyTransferRequest request) {
        return moneyTransferService.transferMoney(request);
    }
}
*/