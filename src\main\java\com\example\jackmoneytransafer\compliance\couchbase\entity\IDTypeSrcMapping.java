package com.example.jackmoneytransafer.compliance.couchbase.entity;

import lombok.Getter;
import lombok.Setter;
import java.util.List;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Setter
@Getter
@Document(collection = "id_type_src_mapping") // Specify the MongoDB collection name
public class IDTypeSrcMapping {

    @Id
    @Field
    private String id;

    @Field
    private String documentUpdateUserId;

    @Field
    private String documentUpdateTimeStamp;

    @Field
    private List<IDTypeMappingDetails> idTypeMappings;  // Assuming a related list of mappings

    // Default Constructor
    public IDTypeSrcMapping() {
    }

    // Parameterized Constructor
    public IDTypeSrcMapping(String id, String documentUpdateUserId, String documentUpdateTimeStamp, List<IDTypeMappingDetails> idTypeMappings) {
        this.id = id;
        this.documentUpdateUserId = documentUpdateUserId;
        this.documentUpdateTimeStamp = documentUpdateTimeStamp;
        this.idTypeMappings = idTypeMappings;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDocumentUpdateUserId() {
        return documentUpdateUserId;
    }

    public void setDocumentUpdateUserId(String documentUpdateUserId) {
        this.documentUpdateUserId = documentUpdateUserId;
    }

    public String getDocumentUpdateTimeStamp() {
        return documentUpdateTimeStamp;
    }

    public void setDocumentUpdateTimeStamp(String documentUpdateTimeStamp) {
        this.documentUpdateTimeStamp = documentUpdateTimeStamp;
    }

    public List<IDTypeMappingDetails> getIdTypeMappings() {
        return idTypeMappings;
    }

    public void setIdTypeMappings(List<IDTypeMappingDetails> idTypeMappings) {
        this.idTypeMappings = idTypeMappings;
    }
}
