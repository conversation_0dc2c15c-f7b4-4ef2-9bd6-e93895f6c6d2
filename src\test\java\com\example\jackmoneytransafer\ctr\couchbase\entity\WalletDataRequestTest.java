package com.example.jackmoneytransafer.ctr.couchbase.entity;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class WalletDataRequestTest {

    private WalletDataRequest walletDataRequest;
    private WalletData walletData;

    @BeforeEach
    void setUp() {
        walletData = new WalletData();
        walletData.setWalletId("WALLET_123");
        walletData.setBalance(new BigDecimal("1000.00"));
        walletData.setCurrency("USD");
        walletData.setStatus("ACTIVE");
        walletData.setCreatedDate(LocalDateTime.now());
        walletData.setLastUpdated(LocalDateTime.now());
        walletData.setOwnerId("USER_123");
        walletData.setWalletType("PERSONAL");

        walletDataRequest = new WalletDataRequest();
        walletDataRequest.setId("REQ_123");
        walletDataRequest.setType("BALANCE_INQUIRY");
        walletDataRequest.setWalletData(walletData);
    }

    @Test
    void testWalletDataRequestCreation() {
        // Assert
        assertNotNull(walletDataRequest);
        assertEquals("REQ_123", walletDataRequest.getId());
        assertEquals("BALANCE_INQUIRY", walletDataRequest.getType());
        assertNotNull(walletDataRequest.getWalletData());
    }

    @Test
    void testWalletDataProperties() {
        // Assert
        WalletData retrievedWalletData = walletDataRequest.getWalletData();
        assertEquals("WALLET_123", retrievedWalletData.getWalletId());
        assertEquals(new BigDecimal("1000.00"), retrievedWalletData.getBalance());
        assertEquals("USD", retrievedWalletData.getCurrency());
        assertEquals("ACTIVE", retrievedWalletData.getStatus());
        assertEquals("USER_123", retrievedWalletData.getOwnerId());
        assertEquals("PERSONAL", retrievedWalletData.getWalletType());
    }

    @Test
    void testWalletDataRequestValidation() {
        // Test null ID validation would fail
        WalletDataRequest invalidRequest = new WalletDataRequest();
        invalidRequest.setId(null);
        invalidRequest.setType("TEST");
        
        // In a real validation scenario, this would fail validation
        assertNull(invalidRequest.getId());
        assertNotNull(invalidRequest.getType());
    }

    @Test
    void testWalletDataRequestEquality() {
        // Create another request with same data
        WalletDataRequest anotherRequest = new WalletDataRequest();
        anotherRequest.setId("REQ_123");
        anotherRequest.setType("BALANCE_INQUIRY");
        anotherRequest.setWalletData(walletData);

        // Test equality (using Lombok generated equals)
        assertEquals(walletDataRequest.getId(), anotherRequest.getId());
        assertEquals(walletDataRequest.getType(), anotherRequest.getType());
    }

    @Test
    void testWalletDataBalanceOperations() {
        // Test balance operations
        BigDecimal originalBalance = walletData.getBalance();
        BigDecimal additionalAmount = new BigDecimal("500.00");
        BigDecimal expectedBalance = originalBalance.add(additionalAmount);

        // Simulate balance update
        walletData.setBalance(expectedBalance);
        walletData.setLastUpdated(LocalDateTime.now());

        assertEquals(expectedBalance, walletData.getBalance());
        assertEquals(new BigDecimal("1500.00"), walletData.getBalance());
    }
}
