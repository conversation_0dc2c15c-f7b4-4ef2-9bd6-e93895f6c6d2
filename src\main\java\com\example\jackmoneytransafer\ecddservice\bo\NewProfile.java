package com.example.jackmoneytransafer.ecddservice.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class NewProfile {

    @JsonProperty("PartyKey")
    private String partyKey;

    @JsonProperty("Transactions")
    private List<com.example.jackmoneytransafer.ecddservice.bo.Transaction> transactions;

    @JsonProperty("DebitCardTransactions")
    private List<DebitCardTransaction> debitCardTransactions;
}

