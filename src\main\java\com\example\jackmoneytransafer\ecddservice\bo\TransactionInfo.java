package com.example.jackmoneytransafer.ecddservice.bo;



import lombok.Data;

@Data
public class TransactionInfo {

    private String currencyCode;
    private String deliveryOptionID;
    private String destinationStateProvince;
    private String destinationCountry;
    private Long eventDateTimeCST;
    private Long eventLocalDateTime;
    private Double faceAmount;
    private Double faceAmountUSD;
    private Double feeAmount;
    private Double feeAmountUSD;
    private String poeTranId;
    private String poeTranEventId;
    private String productName;
    private Double receiveAmount;
    private Double intendedReceiveAmount;
    private Double receiveAmountUSD;
    private Double intendedReceiveAmountUSD;
    private String receiveCountryISO2;
    private String receiveCountryIS03;
    private String receivedCountryCode;
    private String intendedReceiveCurrencyCode;

    private String referenceNumber;
    private String sendCountryISO2;
    private String sendCountryISO3;

    // Transaction timestamps
    private long transactionDate;
    private long transactionDateTime;

    // Status and type
    private String transactionType;
    private String eventStatus;

    // Amend Details
    protected String amendReasonCode;

    // Send Reversal
    protected Double feeRefunded;
    protected String internalReasonCode;
    protected Double reversalAmount;
    protected String sndRvvrslReasonCode;
    protected String reversalTypeCode;

    // Receive Reversal
    protected String rcvRvvrslReasonCode;
    protected Double receiveReversalAmount;
}


