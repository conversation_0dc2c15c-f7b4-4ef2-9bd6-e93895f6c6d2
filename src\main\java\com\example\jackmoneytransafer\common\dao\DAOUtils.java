package com.example.jackmoneytransafer.common.dao;

import javax.xml.crypto.Data;
import java.text.SimpleDateFormat;
import java.util.Calendar;



public abstract class DAOUtils {
    private static final String BOOLEAN_TRUE="Y";
    private static final String BOOLEAN_FALSE="N";

    public static Calendar toCalender(Data data){
        Calendar calender = null;
        if(data != null) {
            calender = Calendar.getInstance();
            calender.setTime(data);
        }
        return calender;
    }
    public static Boolean toBoolean(String value){return BOOLEAN_TRUE.equals(value)? Boolean.TRUE: Boolean.FALSE;}
    public static Boolean toBoolean(int value){return value >0 ? Boolean.TRUE: Boolean.FALSE;}
    public static String toString(Boolean value){ return toString(value != null && value.booleanValue());}
    public static String toString(boolean value){return value ? BOOLEAN_TRUE:BOOLEAN_FALSE;}
    public static int toInt(Boolean value){return value !=null && value.booleanValue()? 1:0;}
    public static Boolean toboolen(String value ,String trueVal,String falseVal){
        if(trueVal !=null)
            return  new Boolean(trueVal.equals(value));
        else if (falseVal != null)
            return  new Boolean(!falseVal.equals(value));
        return toBoolean(value);
    }
    public static String toString (Boolean value,String trueVal,String falseVal){
        if(value != null && value.booleanValue())
            return trueVal;
        return falseVal;
    }

    public static String toString(Calendar calender){
        if(calender ==null)
            return "null";
        SimpleDateFormat dsf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String dataString = dsf.format(calender.getTime());
        return dataString;
    }
}
