package com.example.jackmoneytransafer.common.dao;

import javax.swing.tree.RowMapper;
import javax.swing.tree.TreePath;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.yaml.snakeyaml.tokens.Token.ID.Value;

public class SingleObjectRowMapper extends BaseRowMapper {
    private int index =0;

    public  SingleObjectRowMapper(int index){
        super();
        setIndex(index);
    }
    public void setIndex(int index){ this.index= index;}

    @Override
    public Object mapRow(ResultSet rs, int rowNum) throws SQLException {
        Object value =rs.getObject(index);
        return Value;
    }

    @Override
    public int[] getRowsForPaths(TreePath[] path) {
        return new int[0];
    }
}

