package com.example.jackmoneytransafer.ctr.couchbase.entity;

import jakarta.persistence.Id;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Getter
@Setter
public class WalletDataRequest implements Serializable {
    @Id
    @NotNull
    private String Id;

    @Field("type")
    private String type;

    @Field("walletData")
    private WalletData walletData;
}

