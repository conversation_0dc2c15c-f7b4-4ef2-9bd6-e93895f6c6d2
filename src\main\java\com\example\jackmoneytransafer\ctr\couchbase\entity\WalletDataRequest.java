package com.example.jackmoneytransafer.ctr.couchbase.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Document(collection = "wallet_data_requests")
public class WalletDataRequest implements Serializable {
    @Id
    @NotNull
    private String id;

    @Field("type")
    private String type;

    @Field("walletData")
    private WalletData walletData;
}

