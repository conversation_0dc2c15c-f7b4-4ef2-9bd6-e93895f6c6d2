package com.example.jackmoneytransafer.ecddservice.repository;

import com.example.jackmoneytransafer.ctr.couchbase.entity.WalletDataRequest;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WalletDataRequestRepository extends MongoRepository<WalletDataRequest, String> {
    
    Optional<WalletDataRequest> findByType(String type);
    
    List<WalletDataRequest> findByWalletData_WalletId(String walletId);
    
    List<WalletDataRequest> findByWalletData_Status(String status);
    
    void deleteByType(String type);
}
