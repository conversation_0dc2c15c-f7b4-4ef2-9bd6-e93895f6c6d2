package com.example.jackmoneytransafer.ecddservice.bo;




import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class IFMMergeUnMergeRequest {

    @JsonProperty("MergeUnmergeRequest")
    private MergeUnmergeRequest mergeUnMergeRequest;

    @JsonIgnore
    @JsonProperty("impactedEntities")
    private List<String> impactedEntityList;

    @JsonIgnore
    @JsonProperty("messageGUID")
    private String messageGUID;

    @JsonIgnore
    @JsonProperty("impactedEntity")
    private boolean impactedEntity;

    @JsonIgnore
    @JsonProperty("mergeUnmerge")
    private boolean mergeUnmerge;

    @Data
    public static class MergeUnmergeRequest {
        private String requestId;
        private String entityId;
        private String mergeType;
        private boolean status;
    }
}


