package com.example.jackmoneytransafer.services;



import com.example.jackmoneytransafer.ecddservice.dto.TransactionGenericRequest;
import com.example.jackmoneytransafer.ecddservice.entity.TransactionRequestBO;
import com.example.jackmoneytransafer.ecddservice.repository.TransactionRequestRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class TransactionRequestService {

    private static final Logger logger = LoggerFactory.getLogger(TransactionRequestService.class);

    @Autowired
    private TransactionRequestRepository transactionRequestRepository;

    public TransactionGenericRequest getTransactionGenericRequest(String poeTranEventId) {
        TransactionGenericRequest transactionGenericRequest = null;
        String transactionRequestKey = "TRAN_GEN_REQ_" + poeTranEventId;

        try {
            Optional<TransactionRequestBO> transactionRequestBOOpt =
                    transactionRequestRepository.findByTransactionRequestKey(transactionRequestKey);

            if (transactionRequestBOOpt.isPresent()) {
                TransactionRequestBO transactionRequestBO = transactionRequestBOOpt.get();
                transactionGenericRequest = transactionRequestBO.getTransactionRequest();
                return transactionGenericRequest;
            }
        } catch (Exception e) {
            logger.error("Exception while retrieving the transaction generic request from DB: {}", e.getMessage());
        }
        return null;
    }
}


