package com.example.jackmoneytransafer.ecddservice.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TransactionGenericRequest {
    
    private String transactionId;
    private String senderId;
    private String receiverId;
    private BigDecimal amount;
    private String currency;
    private String transactionType;
    private String status;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    private String referenceNumber;
    private String description;
    
    // Additional fields for generic transaction processing
    private String sourceCountry;
    private String destinationCountry;
    private BigDecimal feeAmount;
    private BigDecimal exchangeRate;
}
