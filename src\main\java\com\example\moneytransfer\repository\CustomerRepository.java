package com.example.moneytransfer.repository;
/*
import com.datastax.oss.driver.shaded.guava.common.base.Optional;
import com.example.moneytransfer.model.Customer;
import org.springframework.data.jpa.repository.JpaRepository;
//import com.example.moneytransfer.model.jpa.CustomerJpa;

import java.util.List;

public interface CustomerRepository extends JpaRepository<Customer, Long>{

    // Find customers by name
    List<Customer> findByName(String name);

    // Find customers by bank name
    List<Customer> findByBankName(String bankName);

    // Find customer by debit card number
    Optional<Customer> findByDebitCardNumber(String debitCardNumber);
}
*/