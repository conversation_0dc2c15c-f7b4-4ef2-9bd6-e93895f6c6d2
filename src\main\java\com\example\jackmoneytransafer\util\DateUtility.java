package com.example.jackmoneytransafer.util;

import javax.xml.datatype.XMLGregorianCalendar;
import java.time.format.DateTimeFormatter;

public class DateUtility {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    
    public static String convertXMLGregorianCalendarToStringGeneral(XMLGregorianCalendar calendar) {
        if (calendar == null) return null;
        return calendar.toGregorianCalendar().toZonedDateTime().format(FORMATTER);
    }
}