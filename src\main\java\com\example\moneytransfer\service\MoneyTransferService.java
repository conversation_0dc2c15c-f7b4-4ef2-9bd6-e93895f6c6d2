package com.example.moneytransfer.service;
/*
import com.example.moneytransfer.dto.MoneyTransferRequest;
import com.example.moneytransfer.model.Customer;
import com.example.moneytransfer.repository.CustomerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;

@Service
public class MoneyTransferService {

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CurrencyExchangeService currencyExchangeService;

    @Transactional
    public String transferMoney(MoneyTransferRequest request) {
        Optional<Customer> senderOpt = customerRepository.findById(request.getSenderId());
        Optional<Customer> receiverOpt = customerRepository.findById(request.getReceiverId());

        if (senderOpt.isEmpty() || receiverOpt.isEmpty()) {
            return "Invalid sender or receiver!";
        }

        Customer sender = senderOpt.get();
        Customer receiver = receiverOpt.get();

        // Convert the amount if needed
        BigDecimal convertedAmount = currencyExchangeService.convert(request.getAmount(), request.getCurrency());

        // Deduct exchange charges
        BigDecimal exchangeCharge = currencyExchangeService.getExchangeCharge(convertedAmount);
        BigDecimal totalDeduction = convertedAmount.add(exchangeCharge);

        if (sender.getBalance().compareTo(totalDeduction) < 0) {
            return "Insufficient funds!";
        }

        // Perform transaction
        sender.setBalance(sender.getBalance().subtract(totalDeduction));
        receiver.setBalance(receiver.getBalance().add(convertedAmount));

        // Save updated balances
        customerRepository.save(sender);
        customerRepository.save(receiver);

        return "Money transferred successfully! Amount: " + convertedAmount + " " + request.getCurrency();
    }
}
*/
