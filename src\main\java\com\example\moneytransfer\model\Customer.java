package com.example.moneytransfer.model;
/*
import jakarta.persistence.*;
import lombok.*;
import org.springframework.data.cassandra.core.mapping.Table;
import java.math.BigDecimal;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table("customers")
public class Customer {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;
    private String bankName;
    private String debitCardNumber;
    private String creditCardNumber;
    private BigDecimal balance; // Customer's account balance
}
*/
