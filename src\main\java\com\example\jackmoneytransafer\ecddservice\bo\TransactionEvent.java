package com.example.jackmoneytransafer.ecddservice.bo;

import lombok.Data;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigDecimal;

@Data
public class TransactionEvent {
    
    private String eventId;
    private String eventType;
    private XMLGregorianCalendar eventCentralStandardTime;
    private BigDecimal usdEquivalentFaceAmount;
    private BigDecimal baseCurrencyFaceAmount;
    private Float usdEquivalentFeeAmount;
    private Float baseCurrencyFeeAmount;
    
    private Consumer consumer;
    private Agent agent;
    
    private String status;
    private String eventStatus;
    private String referenceNumber;
    private String currencyCode;
    private Long poeTranId;
    private String intendedReceiveAgentState;
    
    @Data
    public static class Agent {
        private Long mainOfficeId;
        private Address address;
        private String agentName;
        private String agentCode;
    }
}
