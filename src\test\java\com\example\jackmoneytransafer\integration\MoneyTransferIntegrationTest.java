package com.example.jackmoneytransafer.integration;

import com.example.jackmoneytransafer.ctr.couchbase.entity.WalletData;
import com.example.jackmoneytransafer.ctr.couchbase.entity.WalletDataRequest;
import com.example.jackmoneytransafer.ecddservice.bo.EntityResolutionResponseBO;
import com.example.jackmoneytransafer.ecddservice.bo.IFMMergeUnMergeRequest;
import com.example.jackmoneytransafer.ecddservice.dto.TransactionGenericRequest;
import com.example.jackmoneytransafer.ecddservice.repository.WalletDataRequestRepository;
import com.example.jackmoneytransafer.services.IFMMergeUnMergeService;
import com.example.jackmoneytransafer.services.TransactionRequestService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(properties = {
    "spring.data.mongodb.uri=mongodb://localhost:27017/test_db"
})
class MoneyTransferIntegrationTest {

    @Autowired
    private IFMMergeUnMergeService ifmMergeUnMergeService;

    @Autowired
    private TransactionRequestService transactionRequestService;

    @Autowired
    private WalletDataRequestRepository walletDataRequestRepository;

    private WalletDataRequest testWalletDataRequest;
    private EntityResolutionResponseBO entityResolutionResponseBO;

    @BeforeEach
    void setUp() {
        // Clean up test data
        walletDataRequestRepository.deleteAll();

        // Create test wallet data
        WalletData walletData = new WalletData();
        walletData.setWalletId("TEST_WALLET_123");
        walletData.setBalance(new BigDecimal("1000.00"));
        walletData.setCurrency("USD");
        walletData.setStatus("ACTIVE");
        walletData.setCreatedDate(LocalDateTime.now());
        walletData.setLastUpdated(LocalDateTime.now());
        walletData.setOwnerId("TEST_USER_123");
        walletData.setWalletType("PERSONAL");

        testWalletDataRequest = new WalletDataRequest();
        testWalletDataRequest.setId("TEST_REQ_123");
        testWalletDataRequest.setType("BALANCE_INQUIRY");
        testWalletDataRequest.setWalletData(walletData);

        // Create entity resolution response
        entityResolutionResponseBO = new EntityResolutionResponseBO();
        entityResolutionResponseBO.setMoNumbersList(Collections.singletonList("MO123"));
        entityResolutionResponseBO.setTranEventIDList(Collections.emptyList());
        entityResolutionResponseBO.setEntityId("TEST_ENTITY_123");
        entityResolutionResponseBO.setStatus("SUCCESS");
    }

    @Test
    void testWalletDataRequestPersistence() {
        // Act
        WalletDataRequest savedRequest = walletDataRequestRepository.save(testWalletDataRequest);

        // Assert
        assertNotNull(savedRequest);
        assertNotNull(savedRequest.getId());

        Optional<WalletDataRequest> retrievedRequest = walletDataRequestRepository.findById(savedRequest.getId());
        assertTrue(retrievedRequest.isPresent());
        assertEquals("BALANCE_INQUIRY", retrievedRequest.get().getType());
        assertEquals("TEST_WALLET_123", retrievedRequest.get().getWalletData().getWalletId());
    }

    @Test
    void testMergeUnmergeServiceIntegration() {
        // Arrange
        String entityID = "TEST_ENTITY_123";
        Boolean mergeUnmergeFlag = true;
        Boolean isTest = true;
        String messageGUID = "TEST_MSG_GUID_123";

        // Act
        IFMMergeUnMergeRequest result = ifmMergeUnMergeService.getMergeUnmergeProfile(
                entityID, mergeUnmergeFlag, isTest, Collections.emptyList(),
                Collections.emptyList(), messageGUID, entityResolutionResponseBO);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getMergeUnMergeRequest());
        assertEquals(entityID, result.getMergeUnMergeRequest().getEntityId());
        assertEquals("MERGE", result.getMergeUnMergeRequest().getMergeType());
        assertTrue(result.getMergeUnMergeRequest().isStatus());
    }

    @Test
    void testTransactionRequestServiceIntegration() {
        // This test would require setting up transaction request data
        // For now, testing the null case
        String poeTranEventId = "NON_EXISTENT_EVENT";

        // Act
        TransactionGenericRequest result = transactionRequestService.getTransactionGenericRequest(poeTranEventId);

        // Assert
        assertNull(result); // Should return null for non-existent transaction
    }

    @Test
    void testWalletDataQueryOperations() {
        // Arrange
        walletDataRequestRepository.save(testWalletDataRequest);

        // Act & Assert - Test query by type
        Optional<WalletDataRequest> byType = walletDataRequestRepository.findByType("BALANCE_INQUIRY");
        assertTrue(byType.isPresent());
        assertEquals("TEST_REQ_123", byType.get().getId());

        // Test query by wallet ID
        var byWalletId = walletDataRequestRepository.findByWalletData_WalletId("TEST_WALLET_123");
        assertFalse(byWalletId.isEmpty());
        assertEquals(1, byWalletId.size());

        // Test query by status
        var byStatus = walletDataRequestRepository.findByWalletData_Status("ACTIVE");
        assertFalse(byStatus.isEmpty());
        assertEquals(1, byStatus.size());
    }

    @Test
    void testCompleteMoneyTransferFlow() {
        // This test simulates a complete money transfer flow
        
        // Step 1: Save wallet data request
        WalletDataRequest savedRequest = walletDataRequestRepository.save(testWalletDataRequest);
        assertNotNull(savedRequest);

        // Step 2: Process merge/unmerge request
        IFMMergeUnMergeRequest mergeResult = ifmMergeUnMergeService.getMergeUnmergeProfile(
                "TEST_ENTITY_123", true, true, Collections.emptyList(),
                Collections.emptyList(), "MSG_GUID_123", entityResolutionResponseBO);
        
        assertNotNull(mergeResult);
        assertTrue(mergeResult.getMergeUnMergeRequest().isStatus());

        // Step 3: Verify wallet data is still accessible
        Optional<WalletDataRequest> retrievedRequest = walletDataRequestRepository.findById(savedRequest.getId());
        assertTrue(retrievedRequest.isPresent());
        assertEquals(new BigDecimal("1000.00"), retrievedRequest.get().getWalletData().getBalance());
    }
}
