package com.example.jackmoneytransafer.services;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class ProductMapConfig {
    
    private final Map<String, String> productMappings = new HashMap<>();
    
    public ProductMapConfig() {
        // Initialize with common product mappings
        productMappings.put("SEND", "Money Send");
        productMappings.put("RECEIVE", "Money Receive");
        productMappings.put("TRANSFER", "Money Transfer");
        productMappings.put("EXCHANGE", "Currency Exchange");
    }
    
    public Map<String, String> getProductMappings() {
        return new HashMap<>(productMappings);
    }
    
    public String getProductName(String productCode) {
        return productMappings.get(productCode);
    }
    
    public void addProductMapping(String code, String name) {
        productMappings.put(code, name);
        log.info("Added product mapping: {} -> {}", code, name);
    }
}
