package com.example.jackmoneytransafer.common.dao;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.jdbc.core.support.JdbcDaoSupport;

import java.lang.reflect.Constructor;
import java.sql.SQLException;
import java.util.Map;

public abstract class BaseDAO extends JdbcDaoSupport {

    private static final Logger logger = LoggerFactory.getLogger(BaseDAO.class);
    private String type = null;

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    /**
     * Executes a stored procedure using SimpleJdbcCall.
     */
    protected Map<String, Object> executeJdbcCall(SimpleJdbcCall jdbcCall, MapSqlParameterSource parameters, Map<Integer, Class<?>> errorsMap)
            throws DAOException {
        Map<String, Object> result;
        String message = "Failed to execute the SQL = " + jdbcCall.getProcedureName() +
                " with parameters = " + parameters.getValues();
        try {
            result = jdbcCall.execute(parameters);
        } catch (Exception e) {
            throw createDAOException(errorsMap, message, e);
        }
        return result;
    }

    /**
     * Creates a DAOException based on error mapping.
     */
    protected DAOException createDAOException(Class<?> errorsMap, String message, Exception e) {
        DAOException daoException = null;
        if (e instanceof UncategorizedSQLException) {
            UncategorizedSQLException ue = (UncategorizedSQLException) e;
            SQLException se = ue.getSQLException();
            if (se != null) {
                Class<?> clazz = errorsMap.getName().getClass();
                if (clazz != null) {
                    daoException = createDAOException(clazz, message, e);
                }
            }
        }
        return daoException != null ? daoException : new DAOException(message, e);
    }

    /**
     * Creates an instance of a given class dynamically using reflection.
     */
    protected Object createInstance(Class<?> clazz, Class<?>[] paramTypes, Object... args) throws Exception {
        Constructor<?> constructor = clazz.getConstructor(paramTypes);
        return constructor.newInstance(args);
    }
}
