package com.example.jackmoneytransafer.ecddservice.bo;



public class UniqueRuleInfo extends com.moneygram.screeningaggregator.RuleInfo {

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || !(obj instanceof com.moneygram.screeningaggregator.RuleInfo)) {
            return false;
        }

        com.moneygram.screeningaggregator.RuleInfo ruleInfo = (com.moneygram.screeningaggregator.RuleInfo) obj;

        return getRuleErrorCode() != null &&
                ruleInfo.getRuleErrorCode() != null &&
                getRuleErrorCode().equalsIgnoreCase(ruleInfo.getRuleErrorCode());
    }
}





/*
public class UniqueRuleInfo extends com.moneygram.screeningaggregator.RuleInfo {

    public boolean equals(Object obj) {
        if(this == obj) {
            return true;
        } if(obj == null){
            return false;
        }if (getClass() !=obj.getClass()){
            return false;
        }
        //com.moneygram.screeeningaggregator.RuleInfo ruleInfo = (com.moneygram.screeeningaggregator.RuleInfo) obj;
        if(getRuleErrorCode()! =null && ruleInfo.getRuleErrorCode() !=null && getRuleErrorCode().equalsIgnoreCase(ruleInfo.getRuleErrorCode())){
            return true;
        }
        return false;
    }


}
*/