//package com.example.jackmoneytransafer.ecddservice.bo;


//
//public class UniqueRuleInfo  {
//
//    @Override
//    public boolean equals(Object obj) {
//        if (this == obj) {
//            return true;
//        }
//        if (obj == null || !(obj instanceof UniqueRuleInfo)) {
//            return false;
//        }
//
//        UniqueRuleInfo ruleInfo = (UniqueRuleInfo) obj;
//
//        if (getruleErrorCode() != null && ruleInfo.getRuleErrorCode() != null) {
//            return getRuleErrorCode().equalsIgnoreCase(ruleInfo.getRuleErrorCode());
//        }
//        return false;
//    }
//}





/*
public class UniqueRuleInfo extends com.moneygram.screeningaggregator.RuleInfo {

    public boolean equals(Object obj) {
        if(this == obj) {
            return true;
        } if(obj == null){
            return false;
        }if (getClass() !=obj.getClass()){
            return false;
        }
        //com.moneygram.screeeningaggregator.RuleInfo ruleInfo = (com.moneygram.screeeningaggregator.RuleInfo) obj;
        if(getRuleErrorCode()! =null && ruleInfo.getRuleErrorCode() !=null && getRuleErrorCode().equalsIgnoreCase(ruleInfo.getRuleErrorCode())){
            return true;
        }
        return false;
    }


}
*/