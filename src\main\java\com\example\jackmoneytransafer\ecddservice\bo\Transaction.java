package com.example.jackmoneytransafer.ecddservice.bo;



import lombok.Data;

@Data
public class Transaction {

    private String transactionKey;
    private String transactionNominal;
    private String transactionType;
    private Double requestedAmountNoTax;
    private String cn9;
    private String cs87;
    private String channel;
    private String consumerDOB;
    private String photoID;
    private Integer eventAgentLocation;
    private String oppositeAgentLocation;
    private Long consumerEntityId;
    private Long oppositeConsumerEntityId;
    private String sendCountry;
    private String destinationCountry;
    private String product;
    private String deliveryOption;
    private boolean cb8;
    private boolean cb32;

    // Additional fields mapping
    private String agentAddressState;
    private String agentAddressCity;
    private String agentCountry;
    private String destinationState;
    private String consumerLastName;
    private String consumerAddressCity;
    private Float feeAmountUSD;
    private Double faceAmountLocal;
    private Float feeAmountLocal;
    private String cs10;
    private String oppPartyName;
    private String poeType;
}

