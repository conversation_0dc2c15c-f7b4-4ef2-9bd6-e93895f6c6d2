package com.example.jackmoneytransafer.ecddservice.bo;



import lombok.Data;

import javax.xml.datatype.XMLGregorianCalendar;

@Data
public class Consumer {
    private  String birthCity;
    private String birthCountryISO2;
    private String birthCountryISO3;
    private String consumerAddress;
    private String consumerName;
    private String consumerType;
    private String dateOfBirth;
    private XMLGregorianCalendar dateOfBirthXMLGregorianCal;
    private String emailAddress;
    private String messageField1;
    private String messageField2;
    private String occupation;
    private PersonalID photoID;
    private PersonalID legalID;
    private Phone primaryPhone;
    private Phone secondaryPhone;



}
