package com.example.jackmoneytransafer.ecddservice.bo;



import lombok.Data;
import java.util.List;

@Data
public class EntityTxnRequest {

    private Entity entity;
    private List<String> moHistory;
    private List<String> rtTxnHistory;
    private List<String> cardTxnHistory;
    private boolean nbrLocated;
    private boolean fetchImpactedEntities;
    private String currentEventType;
    private Boolean removeAmendEvents;
    private String messageGUID;

    @Data
    public static class Entity {
        private String entityId;
        private boolean pepFlag;
        private boolean safFlag;
    }
}

