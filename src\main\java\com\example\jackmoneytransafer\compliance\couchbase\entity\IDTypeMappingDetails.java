package com.example.jackmoneytransafer.compliance.couchbase.entity;

import lombok.*;

@Getter
    @Setter
    @EqualsAndHashCode
    @NoArgsConstructor
    @AllArgsConstructor
    public class IDTypeMappingDetails {
        private String dtsIDDocTypeCode;
        private String mfIDDocTypeCode;
        private String iiIDDocTypecode;
        private java.sql.Date beginDate;
        private java.sql.Date endDate;
        private Data createDate;
        private String createUserId;
        private Data updateData;
        private String updateId;


    }
