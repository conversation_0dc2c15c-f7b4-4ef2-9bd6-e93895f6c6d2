# Money Transfer Application

A Spring Boot-based money transfer application with MongoDB integration for handling wallet operations, transaction processing, and entity management.

## Features

- **Wallet Management**: Create and manage digital wallets with balance tracking
- **Transaction Processing**: Handle money transfer requests and transaction history
- **Entity Resolution**: Merge and unmerge entity operations for compliance
- **MongoDB Integration**: Persistent data storage with Spring Data MongoDB
- **Comprehensive Testing**: Unit and integration tests with high coverage

## Technology Stack

- **Java 17**
- **Spring Boot 3.2.0**
- **Spring Data MongoDB**
- **Maven 3.6+**
- **JUnit 5** for testing
- **Mockito** for mocking
- **Lombok** for boilerplate reduction

## Project Structure

```
src/
├── main/java/com/example/jackmoneytransafer/
│   ├── config/                 # Configuration classes
│   ├── ctr/couchbase/entity/   # Entity classes
│   ├── ecddservice/            # Core business objects and services
│   │   ├── bo/                 # Business objects
│   │   ├── dto/                # Data transfer objects
│   │   ├── entity/             # Database entities
│   │   ├── processor/          # Business logic processors
│   │   └── repository/         # Data access repositories
│   ├── services/               # Service layer
│   └── util/                   # Utility classes
└── test/java/                  # Test classes
    ├── integration/            # Integration tests
    └── services/               # Unit tests
```

## Getting Started

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher
- MongoDB 4.4 or higher

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd moneytransfer
   ```

2. **Start MongoDB**
   ```bash
   # Using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   
   # Or start your local MongoDB instance
   mongod
   ```

3. **Build the application**
   ```bash
   mvn clean compile
   ```

4. **Run tests**
   ```bash
   mvn test
   ```

5. **Start the application**
   ```bash
   mvn spring-boot:run
   ```

## Configuration

### Application Properties

```properties
spring.application.name=jackmoneytransfer
spring.data.mongodb.uri=mongodb://localhost:27017/jackmoneytransfer
spring.data.mongodb.auto-index-creation=true
logging.level.com.example.jackmoneytransafer=DEBUG
```

### MongoDB Collections

- `wallet_data_requests`: Wallet operation requests
- `transaction_requests`: Transaction processing data
- `id_type_src_mapping`: ID type mappings for compliance

## API Usage

### Core Services

#### IFMMergeUnMergeService
Handles entity merge and unmerge operations for compliance purposes.

```java
@Autowired
private IFMMergeUnMergeService ifmService;

IFMMergeUnMergeRequest result = ifmService.getMergeUnmergeProfile(
    entityID, mergeFlag, isTest, impactedEntities, 
    cardTxnIds, messageGUID, entityResolution
);
```

#### TransactionRequestService
Manages transaction request processing and retrieval.

```java
@Autowired
private TransactionRequestService transactionService;

TransactionGenericRequest request = transactionService
    .getTransactionGenericRequest(poeTranEventId);
```

#### WalletDataRequestRepository
MongoDB repository for wallet data operations.

```java
@Autowired
private WalletDataRequestRepository walletRepo;

// Find by type
Optional<WalletDataRequest> request = walletRepo.findByType("BALANCE_INQUIRY");

// Find by wallet ID
List<WalletDataRequest> requests = walletRepo
    .findByWalletData_WalletId("WALLET_123");
```

## Testing

### Running Tests

```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=IFMMergeUnMergeServiceTest

# Run integration tests only
mvn test -Dtest=*IntegrationTest

# Generate test coverage report
mvn jacoco:report
```

### Test Categories

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions and database operations
- **Service Tests**: Test business logic and service layer functionality

## Development Guidelines

### Code Style
- Follow Spring Boot conventions
- Use Lombok annotations to reduce boilerplate
- Implement proper error handling and logging
- Write comprehensive tests for all new features

### Logging
```java
@Slf4j
public class YourService {
    public void someMethod() {
        log.info("Processing request with ID: {}", requestId);
        log.error("Error occurred: {}", e.getMessage(), e);
    }
}
```

### Error Handling
```java
try {
    // Business logic
} catch (Exception e) {
    log.error("Error in method: {}", e.getMessage(), e);
    throw new ServiceException("Operation failed", e);
}
```

## Troubleshooting

### Common Issues

1. **MongoDB Connection Issues**
   - Ensure MongoDB is running on port 27017
   - Check connection string in application.properties
   - Verify network connectivity

2. **Test Failures**
   - Ensure test database is clean before running tests
   - Check MongoDB test configuration
   - Verify all required dependencies are available

3. **Build Issues**
   - Clean and rebuild: `mvn clean compile`
   - Check Java version compatibility
   - Verify Maven dependencies

## Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please contact the development team or create an issue in the repository.
