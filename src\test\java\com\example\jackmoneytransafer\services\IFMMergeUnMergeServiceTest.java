package com.example.jackmoneytransafer.services;

import com.example.jackmoneytransafer.ecddservice.bo.EntityResolutionResponseBO;
import com.example.jackmoneytransafer.ecddservice.bo.EntityTxnRequest;
import com.example.jackmoneytransafer.ecddservice.bo.IFMMergeUnMergeRequest;
import com.example.jackmoneytransafer.ecddservice.processor.EntityTxnProcessor;
import com.example.jackmoneytransafer.ecddservice.repository.WalletDataRequestRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IFMMergeUnMergeServiceTest {

    @Mock
    private CountryCacheService countryCacheService;

    @Mock
    private ProductMapConfig productMapConfig;

    @Mock
    private WalletDataRequestRepository walletDataRequestRepository;

    @Mock
    private EntityTxnProcessor entityTxnProcessor;

    @InjectMocks
    private IFMMergeUnMergeService ifmMergeUnMergeService;

    private EntityResolutionResponseBO entityResolutionResponseBO;
    private IFMMergeUnMergeRequest expectedResponse;

    @BeforeEach
    void setUp() {
        entityResolutionResponseBO = new EntityResolutionResponseBO();
        entityResolutionResponseBO.setMoNumbersList(Arrays.asList("MO123", "MO456"));
        entityResolutionResponseBO.setTranEventIDList(Collections.emptyList());

        expectedResponse = new IFMMergeUnMergeRequest();
        IFMMergeUnMergeRequest.MergeUnmergeRequest mergeRequest = new IFMMergeUnMergeRequest.MergeUnmergeRequest();
        mergeRequest.setEntityId("TEST_ENTITY_123");
        mergeRequest.setRequestId("REQ_123");
        mergeRequest.setMergeType("MERGE");
        mergeRequest.setStatus(true);
        expectedResponse.setMergeUnMergeRequest(mergeRequest);
    }

    @Test
    void testGetMergeUnmergeProfile_Success() {
        // Arrange
        String entityID = "TEST_ENTITY_123";
        Boolean mergeUnmergeFlag = true;
        Boolean isTest = false;
        String messageGUID = "MSG_GUID_123";

        when(entityTxnProcessor.callEntityTxnService(any(EntityTxnRequest.class)))
                .thenReturn(expectedResponse);

        // Act
        IFMMergeUnMergeRequest result = ifmMergeUnMergeService.getMergeUnmergeProfile(
                entityID, mergeUnmergeFlag, isTest, Collections.emptyList(), 
                Collections.emptyList(), messageGUID, entityResolutionResponseBO);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResponse.getMergeUnMergeRequest().getEntityId(), 
                result.getMergeUnMergeRequest().getEntityId());
        verify(entityTxnProcessor, times(1)).callEntityTxnService(any(EntityTxnRequest.class));
    }

    @Test
    void testGetMergeUnmergeProfile_NullEntityResolution() {
        // Arrange
        String entityID = "TEST_ENTITY_123";
        Boolean mergeUnmergeFlag = true;
        Boolean isTest = false;
        String messageGUID = "MSG_GUID_123";

        when(entityTxnProcessor.callEntityTxnService(any(EntityTxnRequest.class)))
                .thenReturn(expectedResponse);

        // Act
        IFMMergeUnMergeRequest result = ifmMergeUnMergeService.getMergeUnmergeProfile(
                entityID, mergeUnmergeFlag, isTest, Collections.emptyList(), 
                Collections.emptyList(), messageGUID, null);

        // Assert
        assertNotNull(result);
        verify(entityTxnProcessor, times(1)).callEntityTxnService(any(EntityTxnRequest.class));
    }

    @Test
    void testGetMergeUnmergeProfile_ProcessorReturnsNull() {
        // Arrange
        String entityID = "TEST_ENTITY_123";
        Boolean mergeUnmergeFlag = true;
        Boolean isTest = false;
        String messageGUID = "MSG_GUID_123";

        when(entityTxnProcessor.callEntityTxnService(any(EntityTxnRequest.class)))
                .thenReturn(null);

        // Act
        IFMMergeUnMergeRequest result = ifmMergeUnMergeService.getMergeUnmergeProfile(
                entityID, mergeUnmergeFlag, isTest, Collections.emptyList(), 
                Collections.emptyList(), messageGUID, entityResolutionResponseBO);

        // Assert
        assertNull(result);
        verify(entityTxnProcessor, times(1)).callEntityTxnService(any(EntityTxnRequest.class));
    }

    @Test
    void testGetMergeUnmergeProfile_UnmergeFlag() {
        // Arrange
        String entityID = "TEST_ENTITY_123";
        Boolean mergeUnmergeFlag = false;
        Boolean isTest = false;
        String messageGUID = "MSG_GUID_123";

        IFMMergeUnMergeRequest unmergeResponse = new IFMMergeUnMergeRequest();
        IFMMergeUnMergeRequest.MergeUnmergeRequest unmergeRequest = new IFMMergeUnMergeRequest.MergeUnmergeRequest();
        unmergeRequest.setEntityId("TEST_ENTITY_123");
        unmergeRequest.setMergeType("UNMERGE");
        unmergeRequest.setStatus(true);
        unmergeResponse.setMergeUnMergeRequest(unmergeRequest);

        when(entityTxnProcessor.callEntityTxnService(any(EntityTxnRequest.class)))
                .thenReturn(unmergeResponse);

        // Act
        IFMMergeUnMergeRequest result = ifmMergeUnMergeService.getMergeUnmergeProfile(
                entityID, mergeUnmergeFlag, isTest, Collections.emptyList(), 
                Collections.emptyList(), messageGUID, entityResolutionResponseBO);

        // Assert
        assertNotNull(result);
        assertEquals("UNMERGE", result.getMergeUnMergeRequest().getMergeType());
        verify(entityTxnProcessor, times(1)).callEntityTxnService(any(EntityTxnRequest.class));
    }
}
