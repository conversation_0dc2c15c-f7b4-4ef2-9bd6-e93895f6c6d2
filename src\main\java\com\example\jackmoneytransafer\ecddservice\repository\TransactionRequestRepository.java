package com.example.jackmoneytransafer.ecddservice.repository;

import com.example.jackmoneytransafer.ecddservice.entity.TransactionRequestBO;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TransactionRequestRepository extends MongoRepository<TransactionRequestBO, String> {
    
    Optional<TransactionRequestBO> findByTransactionRequestKey(String transactionRequestKey);
    
    List<TransactionRequestBO> findByStatus(String status);
    
    List<TransactionRequestBO> findByTransactionRequest_TransactionId(String transactionId);
    
    void deleteByTransactionRequestKey(String transactionRequestKey);
}
