package com.example.jackmoneytransafer.ecddservice.bo;



import lombok.Data;

@Data
public abstract class Name {

    protected String firstName;
    protected String middleName;
    protected String lastName;
    protected String lastName2;
    protected String intendedReceiveState;

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getMiddleName() { return middleName; }
    public void setMiddleName(String middleName) { this.middleName = middleName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getLastName2() { return lastName2; }
    public void setLastName2(String lastName2) { this.lastName2 = lastName2; }

    public String getIntendedReceiveState() { return intendedReceiveState; }
    public void setIntendedReceiveState(String intendedReceiveState) {
        this.intendedReceiveState = intendedReceiveState;
    }

    @Override
    public String toString() {
        return "Name{" +
                "firstName='" + firstName + '\'' +
                ", middleName='" + middleName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", lastName2='" + lastName2 + '\'' +
                ", intendedReceiveState='" + intendedReceiveState + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Name)) return false;

        Name name = (Name) o;

        if (getFirstName() != null ? !getFirstName().equals(name.getFirstName()) : name.getFirstName() != null)
            return false;
        if (getMiddleName() != null ? !getMiddleName().equals(name.getMiddleName()) : name.getMiddleName() != null)
            return false;
        if (getLastName() != null ? !getLastName().equals(name.getLastName()) : name.getLastName() != null)
            return false;
        if (getLastName2() != null ? !getLastName2().equals(name.getLastName2()) : name.getLastName2() != null)
            return false;
        return getIntendedReceiveState() != null ?
                getIntendedReceiveState().equals(name.getIntendedReceiveState()) :
                name.getIntendedReceiveState() == null;
    }

    @Override
    public int hashCode() {
        int result = getFirstName() != null ? getFirstName().hashCode() : 0;
        result = 31 * result + (getMiddleName() != null ? getMiddleName().hashCode() : 0);
        result = 31 * result + (getLastName() != null ? getLastName().hashCode() : 0);
        result = 31 * result + (getLastName2() != null ? getLastName2().hashCode() : 0);
        result = 31 * result + (getIntendedReceiveState() != null ? getIntendedReceiveState().hashCode() : 0);
        return result;
    }
}

