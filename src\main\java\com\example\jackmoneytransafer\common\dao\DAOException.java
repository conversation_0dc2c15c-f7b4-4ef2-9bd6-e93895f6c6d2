package com.example.jackmoneytransafer.common.dao;



import java.io.Serial;

public class DAOException extends RuntimeException {
    @Serial
    private static final long serialVersionUID =1;
    public DAOException(String message) {
        super(message);
    }
    public DAOException(String message,Throwable cause){super(message, cause);}

//public DAOException(String message){super(message);}

}
