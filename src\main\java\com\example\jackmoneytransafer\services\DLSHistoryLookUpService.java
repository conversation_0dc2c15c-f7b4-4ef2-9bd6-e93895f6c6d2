package com.example.jackmoneytransafer.services;





import com.example.jackmoneytransafer.ecddservice.bo.TransactionEvent;
import com.example.jackmoneytransafer.ecddservice.bo.EntityResolutionResponseBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DLSHistoryLookUpService {

    @Autowired
    private TpeDaoUtil tpeDaoUtil;

    public List<TransactionEvent> getTransactionHistory(EntityResolutionResponseBO entityResolutionResponseBO) {
        List<String> poeTranEventIDList = null;
        List<TransactionEvent> transactionEventsList = null;

        if (entityResolutionResponseBO != null &&
                entityResolutionResponseBO.getTranEventIDList() != null &&
                !entityResolutionResponseBO.getTranEventIDList().isEmpty()) {

            List<POETranEventID> tranEventIDList = entityResolutionResponseBO.getTranEventIDList();
            poeTranEventIDList = new ArrayList<>();

            StringBuilder strBul = new StringBuilder();
            for (POETranEventID poeTranEventID : tranEventIDList) {
                poeTranEventIDList.add(String.valueOf(poeTranEventID.getATTRVALUE()));
                strBul.append(String.valueOf(poeTranEventID.getATTRVALUE())).append(",");
            }

            log.info("tranEventIDList size in AMLLookup processor is {}", poeTranEventIDList.size());
            log.info("The PoeTranEventIDs are {}", strBul.toString());

            try {
                transactionEventsList = collectTransactionHistory(poeTranEventIDList);
            } catch (Exception e) {
                log.error("Critical Error Exception occurred while fetching history from DLS", e);
            }
        } else {
            log.info("poeTranEventID is null and not calling DLS history lookup");
        }

        return transactionEventsList;
    }

    public List<TransactionEvent> collectTransactionHistory(List<String> tranEventIDList) throws Exception {
        List<TransactionEvent> transactionEventsList = null;

        try {
            transactionEventsList = tpeDaoUtil.collectTransactionHistory(tranEventIDList);
            if (!CollectionUtils.isEmpty(transactionEventsList)) {
                transactionEventsList = removeOldAmendEvents(transactionEventsList);
                log.info("The updated Transaction Event List count is {}", transactionEventsList.size());
            }
        } catch (Exception e) {
            log.error("Exception occurred while fetching history from DLS", e);
        }

        return transactionEventsList;
    }

    public List<TransactionEvent> removeOldAmendEvents(List<TransactionEvent> transactionEventsListMain) {
        List<TransactionEvent> amendedTransactionEvenList = null;
        List<TransactionEvent> finalAmendedTransactionEvenList = null;

        if (transactionEventsListMain != null) {
            amendedTransactionEvenList = new ArrayList<>();
            finalAmendedTransactionEvenList = new ArrayList<>();

            for (TransactionEvent transactionEvent : new ArrayList<>(transactionEventsListMain)) {
                if (!finalAmendedTransactionEvenList.contains(transactionEvent)) {
                    if (transactionEvent != null && transactionEvent.getEventStatus() != null &&
                            "AMD".equalsIgnoreCase(transactionEvent.getEventStatus())) {

                        Long poeTranID = transactionEvent.getPoeTranId();
                        if (poeTranID != null) {
                            for (TransactionEvent transactionEventInner : new ArrayList<>(transactionEventsListMain)) {
                                if (transactionEventInner.getPoeTranId() != null &&
                                        transactionEventInner.getPoeTranId().equals(poeTranID) &&
                                        "AMD".equalsIgnoreCase(transactionEventInner.getEventStatus())) {

                                    amendedTransactionEvenList.add(transactionEventInner);
                                } else if ("SEN".equalsIgnoreCase(transactionEventInner.getEventStatus())) {
                                    transactionEventsListMain.remove(transactionEventInner);
                                    log.info("Removing send event of the latest Amend event {}", transactionEventInner.getPoeTranId());
                                }
                            }
                        }
                    }
                }
            }

            if (!CollectionUtils.isEmpty(amendedTransactionEvenList)) {
                amendedTransactionEvenList.sort(Comparator.comparing(TransactionEvent::getEventCentralStandardTime).reversed());
                finalAmendedTransactionEvenList.add(amendedTransactionEvenList.get(0));
                transactionEventsListMain.removeAll(amendedTransactionEvenList);
            }
        }

        if (finalAmendedTransactionEvenList != null) {
            transactionEventsListMain.addAll(finalAmendedTransactionEvenList);
        }

        return transactionEventsListMain;
    }
}


