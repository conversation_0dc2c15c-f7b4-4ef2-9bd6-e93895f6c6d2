package com.example.jackmoneytransafer.ecddservice.entity;

import com.example.jackmoneytransafer.ecddservice.dto.TransactionGenericRequest;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

@Data
@Document(collection = "transaction_requests")
public class TransactionRequestBO {
    
    @Id
    private String id;
    
    @Field("transactionRequestKey")
    private String transactionRequestKey;
    
    @Field("transactionRequest")
    private TransactionGenericRequest transactionRequest;
    
    @Field("createdDate")
    private LocalDateTime createdDate;
    
    @Field("updatedDate")
    private LocalDateTime updatedDate;
    
    @Field("status")
    private String status;
}
