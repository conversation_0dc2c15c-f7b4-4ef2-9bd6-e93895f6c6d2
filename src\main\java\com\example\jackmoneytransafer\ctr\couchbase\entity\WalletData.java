package com.example.jackmoneytransafer.ctr.couchbase.entity;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class WalletData implements Serializable {
    
    @Field("walletId")
    private String walletId;
    
    @Field("balance")
    private BigDecimal balance;
    
    @Field("currency")
    private String currency;
    
    @Field("status")
    private String status;
    
    @Field("createdDate")
    private LocalDateTime createdDate;
    
    @Field("lastUpdated")
    private LocalDateTime lastUpdated;
    
    @Field("ownerId")
    private String ownerId;
    
    @Field("walletType")
    private String walletType;
}
