package com.example.jackmoneytransafer.ecddservice.bo;

import lombok.Data;

@Data
public class AgentInfo {

    private String agentId;
    private String agentlegacyId;
    private String agentPhoneNumber;
    private String agentPOESequenceNumber;
    private String internalAgentIndicator;
    private String mainOfficeId;
    private String partyId;
    private Boolean pinValidationRequired;
    private Boolean realTimeCreditFlag;
    private String sendReceiveCode;
    private String channelType;
    private AgentAddress rawAddress;
}

