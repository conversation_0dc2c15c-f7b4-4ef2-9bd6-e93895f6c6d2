package com.example.jackmoneytransafer.ecddservice.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;



@Getter
@Setter
public class AlertingestionRequest implements Serializable {
    private String poeTranEventId;
    private List<String> mgiInternalIssueIds;
    private String referenceNumber;
    private String messageGUID;
    private String screeningSubcategoryCode;
    private String screeningAlertActionCode;
    private String screeningAlertReasonCode;
    private String screeningAlertIngestionCode; // Keeping only one field
    private LocalDateTime screeningAlertIngestionDate; // Corrected from DateTime
    private String screeningTransactionType;
    private String alertIngestionRequest;
    private String jobGroupCode;

    // Initialize mgiInternalIssueIds only when accessed
    public List<String> getMgiInternalIssueIds() {
        if (mgiInternalIssueIds == null) {
            mgiInternalIssueIds = new ArrayList<>();
        }
        return mgiInternalIssueIds;
    }
    public void setMgiInternalIssueIds(List<String>mgiInternalIssueIds){
        this.mgiInternalIssueIds = mgiInternalIssueIds;
    }
}
