package com.example.jackmoneytransafer.services;





import com.example.jackmoneytransafer.ecddservice.bo.*;
import com.example.jackmoneytransafer.ecddservice.repository.WalletDataRequestRepository;
import com.example.jackmoneytransafer.ecddservice.processor.EntityTxnProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IFMMergeUnMergeService {

    @Autowired
    private CountryCacheService countryCacheService;

    @Autowired
    private ProductMapConfig productMapConfig;

    @Autowired
    private WalletDataRequestRepository walletDataRequestRepository;

    @Autowired
    private EntityTxnProcessor entityTxnProcessor;

    public IFMMergeUnMergeRequest getMergeUnmergeProfile(
            String entityID, Boolean mergeUnmergeFlag, Boolean isTest,
            List<String> impactedEntitiesList, List<String> cardCashTxnIds, String messageGUID) {

        List<String> moList = Collections.emptyList();

        if (entityResolutionResponseBO != null && entityResolutionResponseBO.getMoNumbersList() != null) {
            moList = entityResolutionResponseBO.getMoNumbersList()
                    .stream().filter(m -> m != null && !m.isEmpty()).collect(Collectors.toList());
        }

        log.info("getMergeUnmergeProfile -> MoList size {}, moNumbers size {}", moList.size(), moNumbers);

        IFMMergeUnMergeRequest ifmMergeUnMergeRequest = null;
        log.info("Inside IFMMergeUnMergeService");

        ifmMergeUnMergeRequest = getMergeUnmergeIFMStr(entityID, entityResolutionResponseBO, mergeUnmergeFlag, moList, messageGUID);

        log.info("The stored proc merge/unmerge request to IFM is {} ", ifmMergeUnMergeRequest);
        if (ifmMergeUnMergeRequest == null) {
            log.error("Critical error, ifmMergeUnmerge is null");
        }

        return ifmMergeUnMergeRequest;
    }

    private IFMMergeUnMergeRequest getMergeUnmergeIFMStr(
            String entityID, EntityResolutionResponseBO entityResolutionResponseBO,
            Boolean mergeUnmergeFlag, List<String> moNumbers, String messageGUID) {

        EntityTxnRequest request = new EntityTxnRequest();
        EntityTxnRequest.Entity entity = new EntityTxnRequest.Entity();
        entity.setEntityId(entityID);
        request.setEntity(entity);
        request.setMoHistory(moNumbers);
        request.setNbrLocated(mergeUnmergeFlag != null ? mergeUnmergeFlag : Boolean.FALSE);
        request.setCurrentEventType(currentEventType);
        request.setFetchImpactedEntities(Boolean.TRUE);
        request.setRemoveAmendEvents(true);
        request.setCardTxnHistory(cardCashTxnIds);
        request.setMessageGUID(messageGUID);

        List<String> poeTranEventIDList = new ArrayList<>();
        if (entityResolutionResponseBO != null && entityResolutionResponseBO.getTranEventIDList() != null) {
            poeTranEventIDList = entityResolutionResponseBO.getTranEventIDList()
                    .stream().map(POETranEventID::getATTRVALUE).collect(Collectors.toList());
        }
        request.setRtTxnHistory(poeTranEventIDList);

        IFMMergeUnMergeRequest ifmMergeUnMergeRequest = entityTxnProcessor.callEntityTxnService(request);

        if (ifmMergeUnMergeRequest == null) {
            log.info("ifmMergeUnMergeRequest is null, not processing further");
        }

        return ifmMergeUnMergeRequest;
    }

    private List<Transaction> getTransactionList(
            List<TransactionEvent> transactionEventList, String currentEventType, String entityID, Map<String, String> countryMap) {

        List<Transaction> transactionsList = new ArrayList<>();

        for (TransactionEvent transactionEvent : transactionEventList) {
            Transaction transaction = new Transaction();

            if (transactionEvent.getEventId() != null) {
                transaction.setTransactionKey("CRR_" + transactionEvent.getEventId());
            }

            if (transactionEvent.getEventCentralStandardTime() != null) {
                String date = DateUtility.convertXMLGregorianCalendarToStringGeneral(transactionEvent.getEventCentralStandardTime());
                transaction.setTransactionNormalizedDateTime(date);
            }

            if (!Strings.isNullOrEmpty(transactionEvent.getEventType())) {
                transaction.setTransactionType(transactionEvent.getEventType());
            }

            if (transactionEvent.getUsdEquivalentFaceAmount() != null) {
                transaction.setRequestedAmountNormalizedCurrency(Double.valueOf(transactionEvent.getUsdEquivalentFaceAmount()));
            }

            if (transactionEvent.getConsumer() != null) {
                String date = DateUtility.convertXMLGregorianCalendarToStringGeneral(transactionEvent.getConsumer().getConsumerDOB());
                transaction.setConsumerDOB(date);
                transaction.setConsumerLastName(transactionEvent.getConsumer().getConsumerLastName());
            }

            if (transactionEvent.getConsumer() != null && transactionEvent.getConsumer().getConsumerAddress() != null) {
                transaction.setConsumerAddressCity(transactionEvent.getConsumer().getConsumerAddress().getCity());
            }

            transaction.setFeeAmountUSD(transactionEvent.getUsdEquivalentFeeAmount());
            transaction.setFaceAmountLocal(transactionEvent.getBaseCurrencyFaceAmount());
            transaction.setFeeAmountLocal(transactionEvent.getBaseCurrencyFeeAmount());

            if (transactionEvent.getAgent() != null && transactionEvent.getAgent().getMainOfficeId() != null) {
                transaction.setCs10(transactionEvent.getAgent().getMainOfficeId() + "");
            }

            if (transactionEvent.getAgent() != null && transactionEvent.getAgent().getAddress() != null && transactionEvent.getAgent().getAddress().getCountry() != null) {
                if (countryMap != null) {
                    transaction.setAgentCountry(countryMap.get(transactionEvent.getAgent().getAddress().getCountry()));
                } else {
                    log.warn("Critical error countryMap is null");
                }
            }

            transaction.setDestinationState(transactionEvent.getIntendedReceiveAgentState());

            transactionsList.add(transaction);
        }

        return transactionsList;
    }
}

