package com.example.moneytransfer.service;

/*
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Service
public class CurrencyExchangeService {

    // Exchange rates (Example: 1 USD = 0.85 EUR, 1 USD = 75 INR)
    private static final Map<String, BigDecimal> exchangeRates = new HashMap<>();

    static {
        exchangeRates.put("USD", BigDecimal.valueOf(1.0));  // Base currency
        exchangeRates.put("EUR", BigDecimal.valueOf(0.85));
        exchangeRates.put("INR", BigDecimal.valueOf(75.0));
    }

    public BigDecimal convert(BigDecimal amount, String currency) {
        if (!exchangeRates.containsKey(currency)) {
            throw new IllegalArgumentException("Unsupported currency: " + currency);
        }
        return amount.multiply(exchangeRates.get(currency));
    }

    public BigDecimal getExchangeCharge(BigDecimal amount) {
        return amount.multiply(BigDecimal.valueOf(0.02)); // 2% exchange charge
    }
}
*/